# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your-supabase-project-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
NEXT_PUBLIC_GOOGLE_CLIENT_ID=your-google-oauth-client-id

# Optional: For server-side operations
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key

# Legacy Backend Environment Variables (can be removed if not using FastAPI backend)
NEXT_PUBLIC_API_URL=http://localhost:8000

# Backend Environment Variables (if keeping FastAPI backend for ML processing)
DATABASE_URL=postgresql://automl_user:automl_pass@localhost:5432/automl_db
REDIS_URL=redis://localhost:6379/0
SECRET_KEY=your-super-secret-key-change-in-production

# Google OAuth (optional)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Development settings
DEBUG=true
ENVIRONMENT=development