import { emailJSService } from '@/lib/email-js';

export interface EmailRequest {
  to: string;
  name: string;
  planType: 'free' | 'pro' | 'enterprise';
}

export const emailService = {
  sendWelcomeEmail: async ({ to, name, planType }: EmailRequest): Promise<boolean> => {
    try {
      console.log(`📧 Sending ${planType} welcome email to ${to}...`);
      
      const success = await emailJSService.sendWelcomeEmail({ to, name, planType });
      
      if (success) {
        console.log(`✅ Welcome email sent successfully to ${to}`);
        return true;
      } else {
        console.error(`❌ Failed to send welcome email to ${to}`);
        return false;
      }
    } catch (error) {
      console.error('Email service error:', error);
      return false;
    }
  }
};