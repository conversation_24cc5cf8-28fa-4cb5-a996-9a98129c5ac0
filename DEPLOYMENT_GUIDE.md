# Deployment Guide - Making Your App Accessible to Others

## Current Problem
Your Google OAuth is configured with `localhost:3000` which only works on your machine. Other users can't access your app.

## Solution: Deploy Your Application

### Step 1: Deploy to Vercel (Free)

1. **Install Vercel CLI:**
   ```bash
   npm i -g vercel
   ```

2. **Deploy your app:**
   ```bash
   vercel
   ```
   
3. **Follow the prompts:**
   - Link to existing project? No
   - Project name: automl-saas-platform
   - Directory: ./
   - Override settings? No

4. **Your app will be deployed to:**
   ```
   https://automl-saas-platform-xyz.vercel.app
   ```

### Step 2: Update Environment Variables in Vercel

1. Go to [Vercel Dashboard](https://vercel.com/dashboard)
2. Select your project
3. Go to Settings > Environment Variables
4. Add these variables:
   ```
   NEXT_PUBLIC_SUPABASE_URL=https://wcoruygibkhmspwlkrid.supabase.co
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
   NEXT_PUBLIC_GOOGLE_CLIENT_ID=y728761427102-c65t614b558iua90m9701tfbk3d4njc8.apps.googleusercontent.com
   ```

### Step 3: Update Google OAuth Configuration

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Navigate to "APIs & Services" > "Credentials"
3. Edit your OAuth 2.0 Client ID
4. **Add your deployed URL to Authorized redirect URIs:**
   ```
   https://wcoruygibkhmspwlkrid.supabase.co/auth/v1/callback
   https://your-app-name.vercel.app/auth/callback
   http://localhost:3000/auth/callback (keep for local development)
   ```

### Step 4: Update Supabase Configuration

1. Go to [Supabase Dashboard](https://supabase.com/dashboard/project/wcoruygibkhmspwlkrid)
2. Navigate to Authentication > URL Configuration
3. **Update Site URL:** `https://your-app-name.vercel.app`
4. **Add Redirect URLs:**
   ```
   https://your-app-name.vercel.app/auth/callback
   http://localhost:3000/auth/callback
   ```

### Step 5: Test Your Deployed App

1. Visit your deployed URL
2. Try Google sign-in
3. Verify it redirects correctly
4. Share the URL with others!

## Alternative: Quick Sharing with ngrok (Temporary)

If you just want to quickly share your local development:

1. **Install ngrok:**
   ```bash
   npm install -g ngrok
   ```

2. **Start your app:**
   ```bash
   npm run dev
   ```

3. **In another terminal, create tunnel:**
   ```bash
   ngrok http 3000
   ```

4. **Add the ngrok URL to Google OAuth:**
   ```
   https://abc123.ngrok.io/auth/callback
   ```

5. **Share the ngrok URL with others**

## Best Practices

### For Production:
- Use a custom domain
- Set up proper SSL certificates
- Configure environment-specific variables
- Set up monitoring and analytics

### For Team Development:
- Each developer should have their own OAuth client
- Use environment-specific configurations
- Set up staging environments

## Security Notes

- Never commit your Google Client Secret to version control
- Use environment variables for all sensitive data
- Regularly rotate your secrets
- Monitor OAuth usage in Google Cloud Console

## Troubleshooting

### Common Issues:
1. **"redirect_uri_mismatch"** - Check that URLs match exactly
2. **"invalid_client"** - Verify Client ID is correct
3. **Environment variables not loading** - Check Vercel dashboard
4. **CORS errors** - Ensure Supabase URLs are configured correctly

### Debug Steps:
1. Check browser console for errors
2. Verify environment variables in Vercel
3. Test OAuth flow step by step
4. Check Supabase logs for authentication errors
