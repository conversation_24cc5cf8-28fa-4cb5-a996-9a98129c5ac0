import emailjs from '@emailjs/browser';

// EmailJS Configuration
const EMAILJS_CONFIG = {
  publicKey: 'x_6ueEx3CRUly-ZVu',
  serviceId: 'service_ff9iykb',
  templateIds: {
    free: 'template_free_welcome',
    pro: 'template_pro_welcome',
    enterprise: 'template_pro_welcome', // Using pro template for enterprise
  },
};

// Initialize EmailJS
let isInitialized = false;

export const initializeEmailJS = () => {
  if (!isInitialized && typeof window !== 'undefined') {
    try {
      emailjs.init({
        publicKey: EMAILJS_CONFIG.publicKey,
      });
      isInitialized = true;
      console.log('✅ EmailJS initialized successfully');
      return true;
    } catch (error) {
      console.error('❌ EmailJS initialization failed:', error);
      return false;
    }
  }
  return isInitialized;
};

export interface EmailRequest {
  to: string;
  name: string;
  planType: 'free' | 'pro' | 'enterprise';
}

export const emailJSService = {
  sendWelcomeEmail: async ({ to, name, planType }: EmailRequest): Promise<boolean> => {
    try {
      console.log(`📧 Starting email send process for ${planType} plan to ${to}...`);
      console.log('📧 EmailJS Config:', EMAILJS_CONFIG);

      // Ensure EmailJS is initialized
      const initialized = initializeEmailJS();

      if (!initialized) {
        console.error('❌ EmailJS not initialized');
        return false;
      }

      console.log('✅ EmailJS initialized successfully');

      // Wait a bit to ensure EmailJS is fully ready
      await new Promise(resolve => setTimeout(resolve, 500));

      const templateId = EMAILJS_CONFIG.templateIds[planType];

      if (!templateId) {
        console.error(`❌ No template found for plan type: ${planType}`);
        console.error('Available templates:', EMAILJS_CONFIG.templateIds);
        return false;
      }

      console.log(`📧 Using template ID: ${templateId} for plan: ${planType}`);

      // Validate email address
      if (!to || !to.includes('@')) {
        console.error('❌ Invalid email address:', to);
        return false;
      }

      // Template parameters that will be sent to EmailJS
      const baseUrl = typeof window !== 'undefined' ? window.location.origin : 'https://automlpro.netlify.app';

      const templateParams = {
        to_email: to, // This is the key field that EmailJS uses
        to_name: name,
        user_name: name,
        plan_type: planType,
        plan_name: planType.charAt(0).toUpperCase() + planType.slice(1),
        from_name: 'AutoML Pro Team',
        reply_to: '<EMAIL>',
        message: `Welcome to AutoML Pro! You've successfully signed up for the ${planType} plan.`,
        site_url: baseUrl,
        dashboard_url: `${baseUrl}/dashboard`,
        pricing_url: `${baseUrl}/pricing`,
        chat_url: `${baseUrl}/chat`,
        // Plan-specific parameters
        plan_price: planType === 'pro' ? '$15/month' : planType === 'enterprise' ? 'Contact Sales' : 'Free',
        cta_url: planType === 'free' ? `${baseUrl}/dashboard` : `${baseUrl}/dashboard`,
        cta_text: planType === 'free' ? 'Start Building Models' : planType === 'pro' ? 'Access Pro Dashboard' : 'Contact Sales Team',
      };

      console.log(`📧 Sending ${planType} welcome email...`);
      console.log('Template params:', {
        to_email: templateParams.to_email,
        to_name: templateParams.to_name,
        plan_type: templateParams.plan_type,
        template_id: templateId
      });

      const result = await emailjs.send(
        EMAILJS_CONFIG.serviceId,
        templateId,
        templateParams
      );

      console.log('EmailJS result:', result);

      if (result.status === 200) {
        console.log(`✅ ${planType} welcome email sent successfully to ${to}`);
        return true;
      } else {
        console.error('❌ EmailJS failed with status:', result.status, result.text);
        return false;
      }

    } catch (error) {
      console.error('❌ Error sending welcome email via EmailJS:', error);
      return false;
    }
  }
};

// Test function to verify EmailJS is working
export const testEmailJS = async (testEmail: string = '<EMAIL>') => {
  try {
    console.log('🧪 Testing EmailJS configuration...');
    console.log('🧪 Config:', EMAILJS_CONFIG);

    const initialized = initializeEmailJS();
    if (!initialized) {
      console.error('❌ EmailJS not initialized');
      return false;
    }

    console.log('✅ EmailJS initialized for test');

    // Use a simple template for testing
    const testParams = {
      to_email: testEmail,
      to_name: 'Test User',
      user_name: 'Test User',
      plan_type: 'free',
      plan_name: 'Free',
      from_name: 'AutoML Pro Team',
      reply_to: '<EMAIL>',
      message: 'This is a test email from AutoML Pro',
      site_url: 'https://automlpro.netlify.app',
      dashboard_url: 'https://automlpro.netlify.app/dashboard',
      pricing_url: 'https://automlpro.netlify.app/pricing',
      chat_url: 'https://automlpro.netlify.app/chat',
      plan_price: 'Free',
      cta_url: 'https://automlpro.netlify.app/dashboard',
      cta_text: 'Start Building Models',
    };

    console.log('🧪 Sending test email with params:', testParams);
    console.log('🧪 Using service ID:', EMAILJS_CONFIG.serviceId);
    console.log('🧪 Using template ID:', 'template_free_welcome');

    const result = await emailjs.send(
      EMAILJS_CONFIG.serviceId,
      'template_free_welcome', // Using your actual free welcome template
      testParams
    );

    console.log('🧪 Test email result:', result);
    return result.status === 200;
  } catch (error) {
    console.error('🧪 Test email failed:', error);
    return false;
  }
};

// Export configuration for debugging
export const getEmailJSConfig = () => EMAILJS_CONFIG;

// Export initialization status
export const getInitializationStatus = () => isInitialized;