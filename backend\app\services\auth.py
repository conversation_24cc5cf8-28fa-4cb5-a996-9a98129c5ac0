from sqlalchemy.orm import Session
from fastapi import HTT<PERSON><PERSON>x<PERSON>, status
from datetime import datetime, timedelta
from passlib.context import Crypt<PERSON>ontext
from jose import jwt
import os

from .. import models, schemas

class AuthService:
    def __init__(self):
        self.pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
        self.secret_key = os.getenv("SECRET_KEY", "your-secret-key")
        self.algorithm = "HS256"
        self.access_token_expire_minutes = 30

    def verify_password(self, plain_password, hashed_password):
        return self.pwd_context.verify(plain_password, hashed_password)

    def get_password_hash(self, password):
        return self.pwd_context.hash(password)

    def create_access_token(self, data: dict):
        to_encode = data.copy()
        expire = datetime.utcnow() + timedelta(minutes=self.access_token_expire_minutes)
        to_encode.update({"exp": expire})
        encoded_jwt = jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
        return encoded_jwt

    async def create_user(self, db: Session, user: schemas.UserCreate):
        # Check if user exists
        db_user = db.query(models.User).filter(models.User.email == user.email).first()
        if db_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already registered"
            )
        
        # Create new user
        hashed_password = self.get_password_hash(user.password)
        db_user = models.User(
            email=user.email,
            name=user.name,
            hashed_password=hashed_password
        )
        db.add(db_user)
        db.commit()
        db.refresh(db_user)
        
        # Create access token
        access_token = self.create_access_token(data={"sub": str(db_user.id)})
        
        return schemas.AuthResponse(
            user=schemas.User.from_orm(db_user),
            token=access_token
        )

    async def authenticate_user(self, db: Session, credentials: schemas.UserLogin):
        user = db.query(models.User).filter(models.User.email == credentials.email).first()
        
        if not user or not self.verify_password(credentials.password, user.hashed_password):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Incorrect email or password"
            )
        
        if not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Inactive user"
            )
        
        # Create access token
        access_token = self.create_access_token(data={"sub": str(user.id)})
        
        return schemas.AuthResponse(
            user=schemas.User.from_orm(user),
            token=access_token
        )