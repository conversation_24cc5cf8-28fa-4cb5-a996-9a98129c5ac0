from sqlalchemy import Column, String, DateTime, Text, JSON, Integer, Float, Foreign<PERSON>ey, Boolean
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID
import uuid
from datetime import datetime
from .database import Base

class User(Base):
    __tablename__ = "users"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    email = Column(String, unique=True, index=True, nullable=False)
    name = Column(String, nullable=False)
    hashed_password = Column(String, nullable=False)
    avatar = Column(String, nullable=True)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    jobs = relationship("PromptJob", back_populates="user")

class PromptJob(Base):
    __tablename__ = "prompt_jobs"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    prompt = Column(Text, nullable=False)
    status = Column(String, default="pending")  # pending, running, completed, failed
    progress = Column(Integer, default=0)
    logs = Column(JSON, default=list)
    error_message = Column(Text, nullable=True)
    result_summary = Column(Text, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    user = relationship("User", back_populates="jobs")
    result = relationship("JobResult", back_populates="job", uselist=False)

class JobResult(Base):
    __tablename__ = "job_results"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    job_id = Column(UUID(as_uuid=True), ForeignKey("prompt_jobs.id"), nullable=False)
    model_type = Column(String, nullable=False)
    accuracy = Column(Float, nullable=False)
    loss = Column(Float, nullable=False)
    training_time = Column(Float, nullable=False)
    dataset_size = Column(Integer, nullable=False)
    features_used = Column(JSON, default=list)
    model_size = Column(String, nullable=False)
    download_url = Column(String, nullable=True)
    api_endpoint = Column(String, nullable=True)
    metrics = Column(JSON, nullable=False)  # precision, recall, f1_score, etc.
    feature_importance = Column(JSON, nullable=True)
    predictions_sample = Column(JSON, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    job = relationship("PromptJob", back_populates="result")