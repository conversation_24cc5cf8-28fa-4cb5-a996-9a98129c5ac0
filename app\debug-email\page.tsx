'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { testEmailJS, getEmailJSConfig, getInitializationStatus } from '@/lib/email-js';
import { emailService } from '@/lib/email';
import { toast } from 'sonner';

export default function DebugEmailPage() {
  const [testEmail, setTestEmail] = useState('<EMAIL>');
  const [testing, setTesting] = useState(false);
  const [logs, setLogs] = useState<string[]>([]);

  const addLog = (message: string) => {
    setLogs(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
    console.log(message);
  };

  const testDirectEmailJS = async () => {
    setTesting(true);
    setLogs([]);
    
    try {
      addLog('🧪 Starting direct EmailJS test...');
      const result = await testEmailJS(testEmail);
      
      if (result) {
        addLog('✅ Direct EmailJS test successful!');
        toast.success('✅ Direct test email sent successfully!');
      } else {
        addLog('❌ Direct EmailJS test failed');
        toast.error('❌ Direct test email failed');
      }
    } catch (error) {
      addLog(`❌ Direct test error: ${error}`);
      toast.error('❌ Direct test failed with error');
    } finally {
      setTesting(false);
    }
  };

  const testWelcomeEmailService = async () => {
    setTesting(true);
    setLogs([]);
    
    try {
      addLog('🧪 Starting welcome email service test...');
      const result = await emailService.sendWelcomeEmail({
        to: testEmail,
        name: 'Test User',
        planType: 'free'
      });
      
      if (result) {
        addLog('✅ Welcome email service test successful!');
        toast.success('✅ Welcome email sent successfully!');
      } else {
        addLog('❌ Welcome email service test failed');
        toast.error('❌ Welcome email failed');
      }
    } catch (error) {
      addLog(`❌ Welcome email test error: ${error}`);
      toast.error('❌ Welcome email test failed with error');
    } finally {
      setTesting(false);
    }
  };

  const testProWelcomeEmail = async () => {
    setTesting(true);
    setLogs([]);
    
    try {
      addLog('🧪 Starting pro welcome email test...');
      const result = await emailService.sendWelcomeEmail({
        to: testEmail,
        name: 'Test Pro User',
        planType: 'pro'
      });
      
      if (result) {
        addLog('✅ Pro welcome email test successful!');
        toast.success('✅ Pro welcome email sent successfully!');
      } else {
        addLog('❌ Pro welcome email test failed');
        toast.error('❌ Pro welcome email failed');
      }
    } catch (error) {
      addLog(`❌ Pro welcome email test error: ${error}`);
      toast.error('❌ Pro welcome email test failed with error');
    } finally {
      setTesting(false);
    }
  };

  const showConfig = () => {
    const config = getEmailJSConfig();
    const isInit = getInitializationStatus();
    
    addLog(`📧 EmailJS Configuration: ${JSON.stringify(config, null, 2)}`);
    addLog(`📧 EmailJS Initialized: ${isInit}`);
    
    toast.info('EmailJS config logged');
  };

  return (
    <div className="min-h-screen bg-background p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Email Debug Page</h1>
        
        <div className="bg-card p-6 rounded-lg border mb-8">
          <h2 className="text-xl font-semibold mb-4">Email Testing</h2>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">Test Email Address</label>
              <Input
                type="email"
                placeholder="Enter test email"
                value={testEmail}
                onChange={(e) => setTestEmail(e.target.value)}
                className="max-w-md"
              />
            </div>
            
            <div className="flex flex-wrap gap-4">
              <Button 
                onClick={testDirectEmailJS}
                disabled={testing}
                variant="outline"
              >
                {testing ? 'Testing...' : 'Test Direct EmailJS'}
              </Button>
              
              <Button 
                onClick={testWelcomeEmailService}
                disabled={testing}
                variant="outline"
              >
                {testing ? 'Testing...' : 'Test Free Welcome Email'}
              </Button>
              
              <Button 
                onClick={testProWelcomeEmail}
                disabled={testing}
                variant="outline"
              >
                {testing ? 'Testing...' : 'Test Pro Welcome Email'}
              </Button>
              
              <Button 
                onClick={showConfig}
                variant="ghost"
              >
                Show Config
              </Button>
            </div>
          </div>
        </div>
        
        <div className="bg-card p-6 rounded-lg border">
          <h2 className="text-xl font-semibold mb-4">Debug Logs</h2>
          <div className="bg-muted p-4 rounded-lg max-h-96 overflow-y-auto">
            {logs.length === 0 ? (
              <p className="text-muted-foreground">No logs yet. Run a test to see debug information.</p>
            ) : (
              <div className="space-y-1">
                {logs.map((log, index) => (
                  <div key={index} className="text-sm font-mono">
                    {log}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
