'use client';

import { useState } from 'react';
import { testEmailJS, getEmailJSConfig, getInitializationStatus } from '@/lib/email-js';
import { emailService } from '@/lib/email';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { toast } from 'sonner';

export function EmailTest() {
  const [testEmail, setTestEmail] = useState('<EMAIL>');
  const [testing, setTesting] = useState(false);

  const handleTestEmailJS = async () => {
    setTesting(true);
    try {
      console.log('🧪 Starting EmailJS test...');
      const result = await testEmailJS(testEmail);
      
      if (result) {
        toast.success('✅ Test email sent successfully! Check your inbox.');
      } else {
        toast.error('❌ Test email failed. Check console for details.');
      }
    } catch (error) {
      console.error('Test error:', error);
      toast.error('❌ Test email failed with error.');
    } finally {
      setTesting(false);
    }
  };

  const handleTestWelcomeEmail = async () => {
    setTesting(true);
    try {
      console.log('🧪 Testing welcome email service...');
      const result = await emailService.sendWelcomeEmail({
        to: testEmail,
        name: 'Test User',
        planType: 'free'
      });
      
      if (result) {
        toast.success('✅ Welcome email sent successfully! Check your inbox.');
      } else {
        toast.error('❌ Welcome email failed. Check console for details.');
      }
    } catch (error) {
      console.error('Welcome email test error:', error);
      toast.error('❌ Welcome email failed with error.');
    } finally {
      setTesting(false);
    }
  };

  const showConfig = () => {
    const config = getEmailJSConfig();
    const isInit = getInitializationStatus();
    
    console.log('📧 EmailJS Configuration:', config);
    console.log('📧 EmailJS Initialized:', isInit);
    
    toast.info('EmailJS config logged to console');
  };

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className="fixed bottom-4 left-4 bg-background border rounded-lg p-4 max-w-sm space-y-3">
      <h3 className="font-bold text-sm">📧 Email Test (Dev Only)</h3>
      
      <Input
        type="email"
        placeholder="Test email address"
        value={testEmail}
        onChange={(e) => setTestEmail(e.target.value)}
        className="text-xs"
      />
      
      <div className="space-y-2">
        <Button 
          onClick={handleTestEmailJS}
          disabled={testing}
          size="sm"
          className="w-full text-xs"
        >
          {testing ? 'Testing...' : 'Test EmailJS Direct'}
        </Button>
        
        <Button 
          onClick={handleTestWelcomeEmail}
          disabled={testing}
          size="sm"
          variant="outline"
          className="w-full text-xs"
        >
          {testing ? 'Testing...' : 'Test Welcome Email'}
        </Button>
        
        <Button 
          onClick={showConfig}
          size="sm"
          variant="ghost"
          className="w-full text-xs"
        >
          Show Config
        </Button>
      </div>
    </div>
  );
}
