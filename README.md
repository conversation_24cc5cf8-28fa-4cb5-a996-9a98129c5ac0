# AutoML Pro - AI-Powered Machine Learning Platform

A complete full-stack SaaS platform that allows users to create machine learning models using natural language prompts. Built with Next.js, Supabase, and includes automated email notifications for user onboarding.

## 🌟 Features

### Frontend
- **Modern UI/UX**: Beautiful, responsive design with Framer Motion animations
- **Dark/Light Mode**: Toggle between themes with smooth transitions
- **Authentication**: Secure login/signup with Supabase Auth and Google OAuth
- **Real-time Updates**: Live job status tracking and progress monitoring
- **Dashboard**: Comprehensive project management and job history
- **Results Visualization**: Detailed model performance metrics and insights

### Backend & Database
- **Supabase**: Complete backend-as-a-service with PostgreSQL database
- **Real-time Subscriptions**: Live updates using Supabase real-time features
- **Row Level Security**: Secure data access with RLS policies
- **Edge Functions**: Serverless functions for email notifications

### Email System
- **Welcome Emails**: Automated emails for new user signups
- **Plan-specific Content**: Different email templates for Free, Pro, and Enterprise plans
- **Payment Notifications**: Email system ready for payment URL distribution
- **Professional Templates**: Beautiful HTML email templates with responsive design

### ML Pipeline (Simulated)
- **Natural Language Processing**: Convert user prompts into ML tasks
- **Background Processing**: Simulated ML training with progress tracking
- **Model Results**: Comprehensive metrics, feature importance, and sample predictions
- **Extensible Architecture**: Ready for real ML framework integration

## 🏗️ Architecture

```
├── Frontend (Next.js)
│   ├── Authentication & User Management
│   ├── Dashboard & Job Management
│   ├── Real-time Status Updates
│   └── Results Visualization
│
├── Backend (Supabase)
│   ├── PostgreSQL Database
│   ├── Authentication & User Management
│   ├── Real-time Subscriptions
│   └── Row Level Security
│
├── Email System (Supabase Edge Functions)
│   ├── Welcome Email Templates
│   ├── Payment Notification System
│   └── Plan-specific Messaging
│
└── Infrastructure
    ├── Supabase Hosting
    ├── Edge Functions
    └── Real-time Database
```

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ and npm
- Supabase account
- Email service provider (optional, for production emails)

### 1. Clone the Repository
```bash
git clone <repository-url>
cd automl-saas-platform
```

### 2. Supabase Setup
1. Create a new Supabase project at [supabase.com](https://supabase.com)
2. Copy your project URL and anon key
3. Run the database migration to set up tables

### 3. Environment Setup
```bash
# Copy environment variables
cp .env.example .env

# Edit .env with your Supabase credentials
NEXT_PUBLIC_SUPABASE_URL=your-supabase-project-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
```

### 4. Install Dependencies and Run
```bash
# Install dependencies
npm install

# Start development server
npm run dev
```

The application will be available at http://localhost:3000

### 5. Deploy Edge Functions (Optional)
```bash
# Install Supabase CLI
npm install -g supabase

# Login to Supabase
supabase login

# Deploy edge functions
supabase functions deploy send-welcome-email
supabase functions deploy send-payment-email
```

## 📧 Email System

### Email Templates
The platform includes three types of welcome emails:

1. **Free Plan Welcome Email**
   - Thanks user for joining
   - Lists free plan features
   - Encourages upgrade to Pro plan
   - Includes getting started guide

2. **Pro Plan Registration Email**
   - Confirms early bird registration
   - Shows locked-in pricing ($15/month)
   - Lists Pro plan features
   - Mentions payment URL coming soon

3. **Enterprise Registration Email**
   - Confirms enterprise interest
   - Lists enterprise features
   - Mentions custom pricing
   - Indicates sales team will contact

### Email Configuration
To enable email sending in production:

1. **Choose an Email Provider**:
   - Resend (recommended)
   - SendGrid
   - AWS SES
   - Mailgun

2. **Update Edge Functions**:
   - Add your email service API key to Supabase secrets
   - Uncomment the email sending code in the edge functions
   - Configure your sender domain

3. **Set Environment Variables**:
   ```bash
   # In Supabase dashboard, add secrets:
   RESEND_API_KEY=your-resend-api-key
   SITE_URL=https://your-domain.com
   ```

## 🎨 UI Components

The frontend includes a comprehensive set of reusable components:

- **Authentication**: Login/signup forms with validation
- **Dashboard**: Job management and statistics
- **Job Status**: Real-time progress tracking
- **Results**: Model performance visualization
- **Navigation**: Responsive header with dark mode toggle
- **Email Popups**: Registration forms with email integration
- **Animations**: Smooth transitions and micro-interactions

## 🔧 Configuration

### Supabase Database Schema
The platform uses the following main tables:
- `profiles`: User profile information
- `jobs`: ML training jobs and status
- `job_results`: Model training results and metrics

### Authentication
- Email/password authentication
- Google OAuth integration
- Automatic profile creation on signup
- Row Level Security for data protection

### Real-time Features
- Live job status updates
- Real-time progress tracking
- Automatic UI updates when data changes

## 📦 Deployment

### Frontend Deployment (Netlify)
```bash
# Build the application
npm run build

# Deploy to Netlify
# The netlify.toml file is already configured
```

### Supabase Configuration
1. Set up your Supabase project
2. Run database migrations
3. Configure authentication providers
4. Deploy edge functions
5. Set up email service integration

## 🔮 Future Enhancements

### Email System Improvements
- [ ] Email template customization
- [ ] Email analytics and tracking
- [ ] Automated email sequences
- [ ] Unsubscribe management
- [ ] Email preferences dashboard

### ML Integration
- [ ] Real ML model training
- [ ] Data upload and processing
- [ ] Model deployment pipeline
- [ ] Performance monitoring

### Business Features
- [ ] Payment processing integration
- [ ] Subscription management
- [ ] Usage tracking and billing
- [ ] Team collaboration features

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit changes: `git commit -m 'Add amazing feature'`
4. Push to branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

- Create an issue for bug reports
- Check the documentation for common questions
- Email support: <EMAIL>

## 🎯 Roadmap

- [x] User authentication and profiles
- [x] Email notification system
- [x] Plan-specific welcome emails
- [x] Real-time job tracking
- [x] Results visualization
- [ ] Payment processing integration
- [ ] Real ML model training
- [ ] Advanced analytics dashboard
- [ ] Team collaboration features
- [ ] Mobile app

---

Built with ❤️ for the AI community. Transform your ideas into intelligent models today!