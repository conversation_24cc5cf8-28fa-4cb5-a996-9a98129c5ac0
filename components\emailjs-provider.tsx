'use client';

import { useEffect, useState } from 'react';
import { initializeEmailJS, getInitializationStatus } from '@/lib/email-js';

export function EmailJSProvider({ children }: { children: React.ReactNode }) {
  const [isReady, setIsReady] = useState(false);

  useEffect(() => {
    // Initialize EmailJS when the app loads
    const initialize = async () => {
      try {
        console.log('🚀 Initializing EmailJS...');
        
        // Wait for window to be fully loaded
        if (typeof window !== 'undefined') {
          const success = initializeEmailJS();
          
          if (success) {
            console.log('✅ EmailJS Provider: Initialization successful');
            setIsReady(true);
          } else {
            console.error('❌ EmailJS Provider: Initialization failed');
            // Retry after a delay
            setTimeout(() => {
              const retrySuccess = initializeEmailJS();
              if (retrySuccess) {
                console.log('✅ EmailJS Provider: Retry initialization successful');
                setIsReady(true);
              }
            }, 2000);
          }
        }
      } catch (error) {
        console.error('❌ EmailJS Provider error:', error);
      }
    };

    initialize();

    // Also check periodically if not initialized
    const checkInterval = setInterval(() => {
      if (!getInitializationStatus()) {
        console.log('🔄 Retrying EmailJS initialization...');
        initializeEmailJS();
      } else if (!isReady) {
        setIsReady(true);
        clearInterval(checkInterval);
      }
    }, 3000);

    return () => clearInterval(checkInterval);
  }, []);

  // Add global debug info
  useEffect(() => {
    if (typeof window !== 'undefined') {
      (window as any).emailJSDebug = {
        isReady,
        isInitialized: getInitializationStatus(),
        config: () => import('@/lib/email-js').then(m => m.getEmailJSConfig()),
      };
    }
  }, [isReady]);

  return <>{children}</>;
}