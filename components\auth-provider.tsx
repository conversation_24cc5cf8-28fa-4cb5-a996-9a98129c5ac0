'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { User, Session } from '@supabase/supabase-js';
import { supabase, Profile } from '@/lib/supabase';
import { emailService } from '@/lib/email';
import { toast } from 'sonner';

interface AuthContextType {
  user: User | null;
  profile: Profile | null;
  session: Session | null;
  loading: boolean;
  signUp: (email: string, password: string, name: string) => Promise<void>;
  signIn: (email: string, password: string) => Promise<void>;
  signInWithGoogle: () => Promise<void>;
  signOut: () => Promise<void>;
  updateProfile: (updates: Partial<Profile>) => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<Profile | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession();
        
        if (error) {
          console.error('Error getting session:', error);
        } else {
          setSession(session);
          setUser(session?.user ?? null);
          
          if (session?.user) {
            await ensureProfileExists(session.user);

            // Send welcome email for initial session too
            console.log('📧 Initial session detected, sending welcome email...');

            // Set flag to prevent duplicate from SIGNED_IN event
            sessionStorage.setItem('welcome_email_sent', 'true');

            setTimeout(async () => {
              await sendWelcomeEmail(session.user);
              // Clear flag after delay
              setTimeout(() => {
                sessionStorage.removeItem('welcome_email_sent');
              }, 5000);
            }, 2000);
          }
        }
      } catch (error) {
        console.error('Error in getInitialSession:', error);
      } finally {
        setLoading(false);
      }
    };

    getInitialSession();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('🔄 Auth state changed:', event, session?.user?.email);
        
        setSession(session);
        setUser(session?.user ?? null);
        
        if (session?.user) {
          // Try to fetch profile, create if missing
          await ensureProfileExists(session.user);

          // Send welcome email to EVERY user who signs in
          // Skip only if this is a pro registration or already sent
          if (event === 'SIGNED_IN') {
            console.log(`📧 User signed in (${event}), checking if welcome email needed...`);

            // Check if this is from a pro registration popup (skip welcome email)
            const isProRegistration = sessionStorage.getItem('pro_registration_in_progress');
            const welcomeEmailSent = sessionStorage.getItem('welcome_email_sent');

            if (!isProRegistration && !welcomeEmailSent) {
              console.log('📧 Sending welcome email for sign-in...');
              // Small delay to ensure everything is ready
              setTimeout(async () => {
                await sendWelcomeEmail(session.user);
              }, 2000);
            } else {
              if (isProRegistration) {
                console.log('🚫 Skipping welcome email - pro registration in progress');
                sessionStorage.removeItem('pro_registration_in_progress');
              }
              if (welcomeEmailSent) {
                console.log('🚫 Skipping welcome email - already sent for this session');
              }
            }
          }
        } else {
          setProfile(null);
        }
        
        setLoading(false);
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  const ensureProfileExists = async (user: User) => {
    try {
      // Try to fetch profile first
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      if (error && error.code === 'PGRST116') {
        // Profile doesn't exist, create it
        console.log('👤 Profile not found, creating profile for:', user.email);

        const userName = user.user_metadata?.name ||
                        user.user_metadata?.full_name ||
                        user.email?.split('@')[0] || 'User';

        await createProfile(user, userName);
      } else if (data) {
        // Profile exists, set it
        setProfile(data);
      }
    } catch (error) {
      console.error('Error ensuring profile exists:', error);
    }
  };

  const sendWelcomeEmail = async (user: User) => {
    try {
      console.log('📧 Sending welcome email to:', user.email);

      if (!user.email) {
        console.log('❌ No email address found for user');
        return;
      }

      // Check if we already sent an email to this user recently
      const emailKey = `welcome_email_${user.email}`;
      const lastSent = sessionStorage.getItem(emailKey);
      const now = Date.now();

      if (lastSent && (now - parseInt(lastSent)) < 60000) { // 1 minute cooldown
        console.log('🚫 Welcome email already sent recently, skipping...');
        return;
      }

      // Get user name from metadata or email
      const userName = user.user_metadata?.name ||
                      user.user_metadata?.full_name ||
                      user.email.split('@')[0] || 'User';

      try {
        const emailSent = await emailService.sendWelcomeEmail({
          to: user.email,
          name: userName,
          planType: 'free',
        });

        if (emailSent) {
          console.log('✅ Welcome email sent successfully');
          // Mark email as sent
          sessionStorage.setItem(emailKey, now.toString());
          toast.success('Welcome! Check your email for a welcome message.');
        } else {
          console.log('⚠️ Welcome email failed to send');
          toast.success('Welcome to AutoML Pro!');
        }
      } catch (emailError) {
        console.error('❌ Welcome email error:', emailError);
        toast.success('Welcome to AutoML Pro!');
      }
    } catch (error) {
      console.error('Error in sendWelcomeEmail:', error);
    }
  };



  const createProfile = async (user: User, name: string) => {
    try {
      console.log('👤 Creating profile for:', user.email);
      
      const { error } = await supabase
        .from('profiles')
        .insert({
          id: user.id,
          email: user.email!,
          name,
          avatar_url: user.user_metadata?.avatar_url,
        });

      if (error) {
        console.error('Error creating profile:', error);
        throw error;
      }

      console.log('✅ Profile created successfully');
      await ensureProfileExists(user);
    } catch (error) {
      console.error('Error creating profile:', error);
      throw error;
    }
  };

  const signUp = async (email: string, password: string, name: string) => {
    try {
      console.log('🚀 Starting email/password signup for:', email);
      
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            name,
          },
        },
      });

      if (error) {
        console.error('Supabase signup error:', error);
        throw error;
      }

      console.log('✅ Supabase signup successful:', data.user?.email);

      if (data.user) {
        console.log('👤 User created successfully, creating profile...');

        // Create profile
        await createProfile(data.user, name);

        console.log('✅ Profile created successfully');

        // Welcome email will be sent by auth state change listener
        toast.success('Account created successfully! Check your email for a welcome message.');
      }
    } catch (error: any) {
      console.error('❌ Signup error:', error);
      toast.error(error.message || 'Failed to create account');
      throw error;
    }
  };

  const signIn = async (email: string, password: string) => {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        throw error;
      }

      toast.success('Welcome back!');
    } catch (error: any) {
      console.error('Error signing in:', error);
      toast.error(error.message || 'Failed to sign in');
      throw error;
    }
  };

  const signInWithGoogle = async () => {
    try {
      console.log('🔗 Starting Google OAuth...');
      
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: typeof window !== 'undefined' ? `${window.location.origin}/auth/callback` : undefined,
          queryParams: {
            access_type: 'offline',
            prompt: 'consent',
          },
        },
      });

      if (error) {
        console.error('Supabase OAuth error:', error);
        throw error;
      }

      console.log('✅ OAuth initiated successfully');
    } catch (error: any) {
      console.error('Error signing in with Google:', error);
      toast.error(error.message || 'Failed to sign in with Google');
      throw error;
    }
  };

  const signOut = async () => {
    try {
      await supabase.auth.signOut();

      setUser(null);
      setProfile(null);
      setSession(null);
      toast.success('Signed out successfully');
    } catch (error: any) {
      console.error('Error signing out:', error);
      toast.error(error.message || 'Failed to sign out');
      throw error;
    }
  };

  const updateProfile = async (updates: Partial<Profile>) => {
    if (!user) {
      throw new Error('No user logged in');
    }

    try {
      const { error } = await supabase
        .from('profiles')
        .update(updates)
        .eq('id', user.id);

      if (error) {
        throw error;
      }

      await ensureProfileExists(user);
      toast.success('Profile updated successfully');
    } catch (error: any) {
      console.error('Error updating profile:', error);
      toast.error(error.message || 'Failed to update profile');
      throw error;
    }
  };

  const value = {
    user,
    profile,
    session,
    loading,
    signUp,
    signIn,
    signInWithGoogle,
    signOut,
    updateProfile,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}